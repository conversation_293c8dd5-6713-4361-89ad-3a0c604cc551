name: <PERSON><PERSON> + Formatter

on:
  pull_request:
    branches: [ main, master ]
  push:
    branches: [ main, master ]

permissions:
  contents: read
  pull-requests: write 

jobs:
  # This job runs on pull requests to check for new linting issues.
  lint-format-pr:
    if: github.event_name == 'pull_request'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8
        with:
          # We need to fetch the full history to compare the PR branch with the base branch.
          fetch-depth: 0

      - name: Set up Go
        uses: actions/setup-go@44694675825211faa026b3c33043df3e48a5fa00
        with:
          go-version-file: "./go.mod"

      - name: Run Formatter
        uses: golangci/golangci-lint-action@4afd733a84b1f43292c63897423277bb7f4313a9
        with:
          version: latest
          only-new-issues: true
          args: fmt

      - name: <PERSON> Linter
        uses: golangci/golangci-lint-action@4afd733a84b1f43292c63897423277bb7f4313a9
        with:
          version: latest
          only-new-issues: true
          args: run
  
  lint-format-master:
    if: github.event_name == 'push'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8
        with:
          # We need to fetch the full history to compare the PR branch with the base branch.
          fetch-depth: 0

      - name: Set up Go
        uses: actions/setup-go@44694675825211faa026b3c33043df3e48a5fa00
        with:
          go-version-file: "./go.mod"

      - name: Run Formatter
        uses: golangci/golangci-lint-action@4afd733a84b1f43292c63897423277bb7f4313a9
        with:
          version: latest
          only-new-issues: true
          args: fmt

      - name: Run Linter
        uses: golangci/golangci-lint-action@4afd733a84b1f43292c63897423277bb7f4313a9
        with:
          version: latest
          only-new-issues: true
          args: run